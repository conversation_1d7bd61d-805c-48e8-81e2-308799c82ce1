{"rustc": 16591470773350601817, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 2741418975728194876, "deps": [[7312356825837975969, "crc32fast", false, 17428703668706974503], [7636735136738807108, "miniz_oxide", false, 5330969615821112097]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\flate2-c6a2d9f16a444b79\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
cargo:rerun-if-env-changed=LIBGIT2_NO_VENDOR
cargo:rerun-if-env-changed=LIBGIT2_NO_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=LIBGIT2_STATIC
cargo:rerun-if-env-changed=LIBGIT2_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_STATIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=LIBGIT2_STATIC
cargo:rerun-if-env-changed=LIBGIT2_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_STATIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-pc-windows-msvc
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_pc_windows_msvc
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:warning=failed to probe system libgit2: Could not run `PKG_CONFIG_ALLOW_SYSTEM_LIBS=1 PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1 pkg-config --libs --cflags libgit2 'libgit2 >= 1.7.2' 'libgit2 < 1.8.0'`
The pkg-config command could not be found.

Most likely, you need to install a pkg-config package for your OS.

If you've already installed it, ensure the pkg-config command is one of the
directories in the PATH environment variable.

If you did not expect this build to link to a pre-installed system library,
then check documentation of the libgit2-sys crate for an option to
build the library from source, or disable features or dependencies
that require pkg-config.
cargo:rustc-cfg=libgit2_vendored
libgit2/include\git2\annotated_commit.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\annotated_commit.h
libgit2/include\git2\apply.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\apply.h
libgit2/include\git2\attr.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\attr.h
libgit2/include\git2\blame.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\blame.h
libgit2/include\git2\blob.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\blob.h
libgit2/include\git2\branch.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\branch.h
libgit2/include\git2\buffer.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\buffer.h
libgit2/include\git2\cert.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\cert.h
libgit2/include\git2\checkout.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\checkout.h
libgit2/include\git2\cherrypick.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\cherrypick.h
libgit2/include\git2\clone.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\clone.h
libgit2/include\git2\commit.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\commit.h
libgit2/include\git2\common.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\common.h
libgit2/include\git2\config.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\config.h
libgit2/include\git2\credential.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\credential.h
libgit2/include\git2\credential_helpers.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\credential_helpers.h
libgit2/include\git2\cred_helpers.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\cred_helpers.h
libgit2/include\git2\deprecated.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\deprecated.h
libgit2/include\git2\describe.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\describe.h
libgit2/include\git2\diff.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\diff.h
libgit2/include\git2\email.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\email.h
libgit2/include\git2\errors.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\errors.h
libgit2/include\git2\experimental.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\experimental.h
libgit2/include\git2\filter.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\filter.h
libgit2/include\git2\global.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\global.h
libgit2/include\git2\graph.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\graph.h
libgit2/include\git2\ignore.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\ignore.h
libgit2/include\git2\index.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\index.h
libgit2/include\git2\indexer.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\indexer.h
libgit2/include\git2\mailmap.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\mailmap.h
libgit2/include\git2\merge.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\merge.h
libgit2/include\git2\message.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\message.h
libgit2/include\git2\net.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\net.h
libgit2/include\git2\notes.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\notes.h
libgit2/include\git2\object.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\object.h
libgit2/include\git2\odb.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\odb.h
libgit2/include\git2\odb_backend.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\odb_backend.h
libgit2/include\git2\oid.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\oid.h
libgit2/include\git2\oidarray.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\oidarray.h
libgit2/include\git2\pack.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\pack.h
libgit2/include\git2\patch.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\patch.h
libgit2/include\git2\pathspec.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\pathspec.h
libgit2/include\git2\proxy.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\proxy.h
libgit2/include\git2\rebase.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\rebase.h
libgit2/include\git2\refdb.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\refdb.h
libgit2/include\git2\reflog.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\reflog.h
libgit2/include\git2\refs.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\refs.h
libgit2/include\git2\refspec.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\refspec.h
libgit2/include\git2\remote.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\remote.h
libgit2/include\git2\repository.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\repository.h
libgit2/include\git2\reset.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\reset.h
libgit2/include\git2\revert.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\revert.h
libgit2/include\git2\revparse.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\revparse.h
libgit2/include\git2\revwalk.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\revwalk.h
libgit2/include\git2\signature.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\signature.h
libgit2/include\git2\stash.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\stash.h
libgit2/include\git2\status.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\status.h
libgit2/include\git2\stdint.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\stdint.h
libgit2/include\git2\strarray.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\strarray.h
libgit2/include\git2\submodule.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\submodule.h
libgit2/include\git2\sys\alloc.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\alloc.h
libgit2/include\git2\sys\commit.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\commit.h
libgit2/include\git2\sys\commit_graph.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\commit_graph.h
libgit2/include\git2\sys\config.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\config.h
libgit2/include\git2\sys\cred.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\cred.h
libgit2/include\git2\sys\credential.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\credential.h
libgit2/include\git2\sys\diff.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\diff.h
libgit2/include\git2\sys\email.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\email.h
libgit2/include\git2\sys\filter.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\filter.h
libgit2/include\git2\sys\hashsig.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\hashsig.h
libgit2/include\git2\sys\index.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\index.h
libgit2/include\git2\sys\mempack.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\mempack.h
libgit2/include\git2\sys\merge.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\merge.h
libgit2/include\git2\sys\midx.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\midx.h
libgit2/include\git2\sys\odb_backend.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\odb_backend.h
libgit2/include\git2\sys\openssl.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\openssl.h
libgit2/include\git2\sys\path.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\path.h
libgit2/include\git2\sys\refdb_backend.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\refdb_backend.h
libgit2/include\git2\sys\reflog.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\reflog.h
libgit2/include\git2\sys\refs.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\refs.h
libgit2/include\git2\sys\remote.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\remote.h
libgit2/include\git2\sys\repository.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\repository.h
libgit2/include\git2\sys\stream.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\stream.h
libgit2/include\git2\sys\transport.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\sys\transport.h
libgit2/include\git2\tag.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\tag.h
libgit2/include\git2\trace.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\trace.h
libgit2/include\git2\transaction.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\transaction.h
libgit2/include\git2\transport.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\transport.h
libgit2/include\git2\tree.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\tree.h
libgit2/include\git2\types.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\types.h
libgit2/include\git2\version.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\version.h
libgit2/include\git2\worktree.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2\worktree.h
libgit2/include\git2.h => C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\include\git2.h
OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\deps;C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\PowerShell\7;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\CMake\bin;C:\Program Files (x86)\Android\android-sdk\emulator;C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\PowerShell\7;C:\Windows\System32;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Go\bin;C:\Program Files\Git\cmd;C:\Program Files\PowerShell\7\;C:\Users\<USER>\.cargo\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\PATH_PROGRAMS;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;C:\Users\<USER>\sdks\flutter_sdk\flutter\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\go\bin;d:\wpsystem\s-1-5-21-2481790359-1700336622-2322669134-1002\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\scripts;c:\users\<USER>\.local\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
annotated_commit.c
apply.c
attr.c
attr_file.c
attrcache.c
blame.c
blame_git.c
blob.c
checkout.c
branch.c
cache.c
buf.c
cherrypick.c
commit_graph.c
clone.c
commit.c
commit_list.c
config.c
config_cache.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
config_entries.c
config_file.c
config_mem.c
config_snapshot.c
crlf.c
config_parse.c
describe.c
delta.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
diff.c
diff_driver.c
diff_generate.c
diff_file.c
diff_print.c
diff_parse.c
diff_stats.c
diff_tform.c
diff_xdiff.c
email.c
errors.c
fetchhead.c
fetch.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
filter.c
grafts.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
graph.c
hashsig.c
ident.c
idxmap.c
iterator.c
ignore.c
indexer.c
libgit2.c
index.c
mailmap.c
merge.c
merge_file.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
object.c
mwindow.c
merge_driver.c
notes.c
message.c
object_api.c
midx.c
odb.c
odb_pack.c
odb_mempack.c
odb_loose.c
offmap.c
oidarray.c
oid.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
oidmap.c
pack-objects.c
pack.c
parse.c
patch_generate.c
patch.c
patch_parse.c
path.c
pathspec.c
push.c
proxy.c
reader.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
rebase.c
refdb.c
libgit2/src/libgit2\patch_generate.c(65): warning C5287: operands are different enum types 'git_diff_flag_t' and '<unnamed-enum-GIT_DIFF_FLAG__FREE_PATH>'; use an explicit cast to silence this warning
libgit2/src/libgit2\patch_generate.c(66): warning C5287: operands are different enum types 'git_diff_flag_t' and '<unnamed-enum-GIT_DIFF_FLAG__FREE_PATH>'; use an explicit cast to silence this warning
refdb_fs.c
reflog.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
refs.c
remote.c
refspec.c
repository.c
reset.c
revert.c
revparse.c
revwalk.c
signature.c
stash.c
status.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
strarray.c
submodule.c
sysdir.c
trace.c
tag.c
threadstate.c
trailer.c
transaction.c
transport.c
tree-cache.c
libgit2/src/libgit2\submodule.c(1433): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1558): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1569): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1577): warning C5287: operands are different enum types '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>' and 'git_submodule_status_t'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1615): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1629): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1648): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1659): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
libgit2/src/libgit2\submodule.c(1699): warning C5287: operands are different enum types 'git_submodule_status_t' and '<unnamed-enum-GIT_SUBMODULE_STATUS__WD_SCANNED>'; use an explicit cast to silence this warning
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
worktree.c
alloc.c
tree.c
date.c
filebuf.c
fs_path.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
futils.c
hash.c
net.c
pool.c
posix.c
pqueue.c
rand.c
runtime.c
regexp.c
sortedcache.c
str.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
strmap.c
thread.c
tsort.c
utf8.c
util.c
varint.c
vector.c
wildmatch.c
auth_gssapi.c
zstream.c
auth.c
auth_ntlmclient.c
auth_sspi.c
credential.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
credential_helpers.c
git.c
http.c
httpclient.c
smart_pkt.c
smart.c
local.c
winhttp.c
mbedtls.c
smart_protocol.c
ssh.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
openssl.c
openssl_dynamic.c
openssl_legacy.c
socket.c
schannel.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
registry.c
http_parser.c
tls.c
xdiffi.c
xemit.c
stransport.c
xhistogram.c
xmerge.c
xpatience.c
xprepare.c
xutils.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
pcre_byte_order.c
pcre_chartables.c
pcre_compile.c
pcre_config.c
pcre_dfa_exec.c
pcre_exec.c
pcre_fullinfo.c
pcre_globals.c
pcre_get.c
pcre_maketables.c
pcre_jit_compile.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
pcre_newline.c
pcre_refcount.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
pcre_ord2utf8.c
pcre_printint.c
pcre_string_utils.c
pcre_tables.c
pcre_study.c
pcre_ucd.c
pcre_valid_utf8.c
pcre_version.c
pcreposix.c
stdalloc.c
pcre_xclass.c
error.c
dir.c
failalloc.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
map.c
posix_w32.c
path_w32.c
precompiled.c
utf-conv.c
thread.c
w32_buffer.c
w32_leakcheck.c
collisiondetect.c
sha1.c
w32_util.c
ubc_check.c
win32.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=git2
cargo:rustc-link-search=native=C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out\build
cargo:root=C:\Users\<USER>\Downloads\projects\minecraft\Gitrinth\target\release\build\libgit2-sys-f892b7431175787b\out
cargo:rustc-link-lib=winhttp
cargo:rustc-link-lib=rpcrt4
cargo:rustc-link-lib=ole32
cargo:rustc-link-lib=crypt32
cargo:rustc-link-lib=secur32
cargo:rerun-if-changed=libgit2/include
cargo:rerun-if-changed=libgit2/src
cargo:rerun-if-changed=libgit2/deps

{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2040997289075261528, "path": 1264441920063201611, "deps": [[9620753569207166497, "zerovec_derive", false, 33344693698980077], [10706449961930108323, "yoke", false, 17604774144065061204], [17046516144589451410, "zerofrom", false, 5384541218563603240]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zerovec-59938185d3e7763c\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
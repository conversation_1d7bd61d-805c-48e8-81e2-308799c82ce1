{"rustc": 16591470773350601817, "features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"vendored\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 8268026152342382216, "profile": 2040997289075261528, "path": 14951616230566899740, "deps": [[4684437522915235464, "libc", false, 4742545423155068834], [11857865261928459945, "build_script_build", false, 16378019006115737901], [17022423707615322322, "libz_sys", false, 15685289868781876547], [17372192778073352438, "libssh2_sys", false, 16305573990871903434]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\libgit2-sys-3c738ce7bc54dc05\\dep-lib-libgit2_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
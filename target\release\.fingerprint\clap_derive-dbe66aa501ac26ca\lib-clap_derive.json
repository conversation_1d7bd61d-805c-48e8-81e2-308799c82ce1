{"rustc": 16591470773350601817, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 12613628788268674035, "path": 8234860570034207077, "deps": [[3060637413840920116, "proc_macro2", false, 12643830767369123242], [4974441333307933176, "syn", false, 5239925405773185111], [13077543566650298139, "heck", false, 7287727586112501445], [17990358020177143287, "quote", false, 17561921140747888538]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_derive-dbe66aa501ac26ca\\dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "[\"default\", \"https\", \"openssl-probe\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"default\", \"https\", \"openssl-probe\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"unstable\", \"vendored-libgit2\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 17727337184649825680, "profile": 2040997289075261528, "path": 14720087027587149090, "deps": [[3150220818285335163, "url", false, 16069633636847729873], [4684437522915235464, "libc", false, 4742545423155068834], [5986029879202738730, "log", false, 7548405875655827093], [7896293946984509699, "bitflags", false, 12704244422798907352], [11857865261928459945, "libgit2_sys", false, 3855568935947189634]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\git2-6a22ba735780d72e\\dep-lib-git2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
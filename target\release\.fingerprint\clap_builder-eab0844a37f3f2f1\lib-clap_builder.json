{"rustc": 16591470773350601817, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 9417829390277217133, "deps": [[5820056977320921005, "anstream", false, 10564024475581833826], [9394696648929125047, "anstyle", false, 6657225289157800519], [11166530783118767604, "strsim", false, 10561447142744923346], [11649982696571033535, "clap_lex", false, 17521039089354264848]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_builder-eab0844a37f3f2f1\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11857865261928459945, "build_script_build", false, 5361567354229157202], [17372192778073352438, "build_script_build", false, 9495967210069263403], [17022423707615322322, "build_script_build", false, 5804316581947660604]], "local": [{"RerunIfChanged": {"output": "release\\build\\libgit2-sys-f892b7431175787b\\output", "paths": ["libgit2/include", "libgit2/src", "libgit2/deps"]}}, {"RerunIfEnvChanged": {"var": "LIBGIT2_NO_VENDOR", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBGIT2_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBGIT2_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBGIT2_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBGIT2_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBGIT2_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VSTEL_MSBuildProjectFullPath", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-msvc", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}
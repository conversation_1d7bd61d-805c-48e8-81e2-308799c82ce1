{"rustc": 16591470773350601817, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 5627820096486484124, "path": 7667950157907367914, "deps": [[7911289239703230891, "adler2", false, 456056481555003333]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-08d57ffd8421ae71\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
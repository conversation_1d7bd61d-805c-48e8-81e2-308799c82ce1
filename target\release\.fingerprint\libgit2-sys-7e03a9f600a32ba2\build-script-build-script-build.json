{"rustc": 16591470773350601817, "features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"vendored\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 17883862002600103897, "profile": 1369601567987815722, "path": 11280748219410702593, "deps": [[3214373357989284387, "pkg_config", false, 17503284289360506383], [15056754423999335055, "cc", false, 12139712411089733008]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\libgit2-sys-7e03a9f600a32ba2\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}